# Gitea Mirror deployment configuration
# Standard deployment with automatic database maintenance

services:
  gitea-mirror:
    image: ${DOCKER_REGISTRY:-ghcr.io}/${DOCKER_IMAGE:-arunavo4/gitea-mirror}:${DOCKER_TAG:-latest}
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
      cache_from:
        - ${DOCKER_REGISTRY:-ghcr.io}/${DOCKER_IMAGE:-arunavo4/gitea-mirror}:${DOCKER_TAG:-latest}
    container_name: gitea-mirror
    restart: unless-stopped
    ports:
      - "4321:4321"
    volumes:
      - gitea-mirror-data:/app/data
      # Mount CA certificates for self-signed Gitea instances
      # Option 1: Mount specific CA certificate
      #- ./ca-certificates/your-ca.crt:/usr/local/share/ca-certificates/your-ca.crt:ro
      # Option 2: Mount system CA bundle (if your CA is already in system store)
      #- /etc/ssl/certs/ca-certificates.crt:/etc/ssl/certs/ca-certificates.crt:ro
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:data/gitea-mirror.db
      - HOST=0.0.0.0
      - PORT=4321
      - JWT_SECRET=${JWT_SECRET:-your-secret-key-change-this-in-production}
      # GitHub/Gitea Mirror Config
      - GITHUB_USERNAME=${GITHUB_USERNAME:-}
      - GITHUB_TOKEN=${GITHUB_TOKEN:-}
      - SKIP_FORKS=${SKIP_FORKS:-false}
      - PRIVATE_REPOSITORIES=${PRIVATE_REPOSITORIES:-false}
      - MIRROR_ISSUES=${MIRROR_ISSUES:-false}
      - MIRROR_WIKI=${MIRROR_WIKI:-false}
      - MIRROR_STARRED=${MIRROR_STARRED:-false}
      - MIRROR_ORGANIZATIONS=${MIRROR_ORGANIZATIONS:-false}
      - PRESERVE_ORG_STRUCTURE=${PRESERVE_ORG_STRUCTURE:-false}
      - ONLY_MIRROR_ORGS=${ONLY_MIRROR_ORGS:-false}
      - SKIP_STARRED_ISSUES=${SKIP_STARRED_ISSUES:-false}
      - GITEA_URL=${GITEA_URL:-}
      - GITEA_TOKEN=${GITEA_TOKEN:-}
      - GITEA_USERNAME=${GITEA_USERNAME:-}
      - GITEA_ORGANIZATION=${GITEA_ORGANIZATION:-github-mirrors}
      - GITEA_ORG_VISIBILITY=${GITEA_ORG_VISIBILITY:-public}
      - DELAY=${DELAY:-3600}
      # SSL/TLS Configuration for self-signed certificates
      # WARNING: Only use NODE_TLS_REJECT_UNAUTHORIZED=0 for development/testing
      # For production, mount your CA certificate using the volumes section above
      #- NODE_TLS_REJECT_UNAUTHORIZED=0
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=3", "--spider", "http://localhost:4321/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

# Define named volumes for database persistence
volumes:
  gitea-mirror-data:    # Database volume
