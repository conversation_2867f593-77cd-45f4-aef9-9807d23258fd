{"name": "gitea-mirror", "type": "module", "version": "2.18.0", "engines": {"bun": ">=1.2.9"}, "scripts": {"setup": "bun install && bun run manage-db init", "dev": "bunx --bun astro dev", "dev:clean": "bun run cleanup-db && bun run manage-db init && bunx --bun astro dev", "build": "bunx --bun astro build", "cleanup-db": "rm -f gitea-mirror.db data/gitea-mirror.db", "manage-db": "bun scripts/manage-db.ts", "init-db": "bun scripts/manage-db.ts init", "check-db": "bun scripts/manage-db.ts check", "fix-db": "bun scripts/manage-db.ts fix", "reset-users": "bun scripts/manage-db.ts reset-users", "startup-recovery": "bun scripts/startup-recovery.ts", "startup-recovery-force": "bun scripts/startup-recovery.ts --force", "test-recovery": "bun scripts/test-recovery.ts", "test-recovery-cleanup": "bun scripts/test-recovery.ts --cleanup", "test-shutdown": "bun scripts/test-graceful-shutdown.ts", "test-shutdown-cleanup": "bun scripts/test-graceful-shutdown.ts --cleanup", "preview": "bunx --bun astro preview", "start": "bun dist/server/entry.mjs", "start:fresh": "bun run cleanup-db && bun run manage-db init && bun dist/server/entry.mjs", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "astro": "bunx --bun astro"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.3.0", "@astrojs/node": "^9.2.2", "@astrojs/react": "^4.3.0", "@octokit/rest": "^22.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-virtual": "^3.13.10", "@types/canvas-confetti": "^1.9.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.9.3", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "drizzle-orm": "^0.44.2", "fuse.js": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.515.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "uuid": "^11.1.0", "zod": "^3.25.64"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.5.2", "jsdom": "^26.1.0", "tsx": "^4.20.3", "ultracite": "5.0.32", "vitest": "^3.2.3"}, "packageManager": "bun@1.2.16"}