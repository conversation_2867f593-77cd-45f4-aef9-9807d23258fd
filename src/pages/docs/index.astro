---
import MainLayout from '../../layouts/main.astro';
import { LuSettings, LuRocket, LuBookOpen } from 'react-icons/lu';

// Define our documentation pages directly
const docs = [
  {
    slug: 'architecture',
    title: 'Architecture',
    description: 'Comprehensive overview of the Gitea Mirror application architecture.',
    order: 1,
    icon: Lu<PERSON><PERSON><PERSON><PERSON>,
    href: '/docs/architecture'
  },
  {
    slug: 'configuration',
    title: 'Configuration',
    description: 'Guide to configuring Gitea Mirror for your environment.',
    order: 2,
    icon: LuSettings,
    href: '/docs/configuration'
  },
  {
    slug: 'quickstart',
    title: 'Quick Start Guide',
    description: 'Get started with Gitea Mirror quickly.',
    order: 3,
    icon: LuRocket,
    href: '/docs/quickstart'
  }
];

// Sort by order
const sortedDocs = docs.sort((a, b) => a.order - b.order);
---

<MainLayout title="Documentation">
  <main class="max-w-5xl mx-auto px-4 py-12">
    <h1 class="text-3xl font-bold mb-2 text-center text-foreground">Gitea Mirror Documentation</h1>
    <p class="mb-10 text-lg text-muted-foreground text-center">Browse guides and technical docs for Gitea Mirror.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      {sortedDocs.map(doc => {
        const Icon = doc.icon;
        
        return (
          <a
            href={doc.href}
            class="group block p-7 border border-border rounded-2xl bg-card hover:bg-muted transition-colors shadow-lg focus:ring-2 focus:ring-ring outline-none"
            tabindex="0"
          >
            <div class="flex items-center gap-3 mb-2">
              <div class="w-10 h-10 bg-muted rounded-full flex items-center justify-center text-muted-foreground">
                <Icon className="w-5 h-5" />
              </div>
              <h2 class="text-xl font-semibold group-hover:text-foreground transition">{doc.title}</h2>
            </div>
            <p class="text-muted-foreground">{doc.description}</p>
          </a>
        );
      })}
    </div>
  </main>
</MainLayout>