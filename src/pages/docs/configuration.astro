---
import MainLayout from '../../layouts/main.astro';

const envVars = [
  { name: 'NODE_ENV', desc: 'Runtime environment', default: 'development', example: 'production' },
  { name: 'DATABASE_URL', desc: 'SQLite database URL', default: 'file:data/gitea-mirror.db', example: 'file:path/to/database.db' },
  { name: 'JWT_SECRET', desc: 'Secret key for JWT auth', default: 'Auto-generated', example: 'your-secure-string' },
  { name: 'HOST', desc: 'Server host', default: 'localhost', example: '0.0.0.0' },
  { name: 'PORT', desc: 'Server port', default: '4321', example: '8080' }
];

const githubOptions = [
  { name: 'Username', desc: 'Your GitHub username', default: '-' },
  { name: 'Token', desc: 'GitHub personal access token (Classic PAT)', default: '-' },
  { name: 'Private Repositories', desc: 'Include private repositories', default: 'false' },
  { name: 'Mirror Starred', desc: 'Mirror repositories you\'ve starred', default: 'false' },
  { name: 'Mirror Issues', desc: 'Mirror issues from GitHub to Gitea', default: 'false' },
  { name: 'Mirror Wiki', desc: 'Mirror wiki pages from GitHub to Gitea', default: 'false' },
  { name: 'Mirror Organizations', desc: 'Mirror organization repositories', default: 'false' },
  { name: 'Only Mirror Orgs', desc: 'Only mirror organization repositories', default: 'false' },
  { name: 'Skip Forks', desc: 'Exclude repositories that are forks', default: 'false' },
  { name: 'Skip Starred Issues', desc: 'Skip issues for starred repositories', default: 'false' }
];

const giteaOptions = [
  { name: 'URL', desc: 'Gitea server URL', default: '-' },
  { name: 'Token', desc: 'Gitea access token', default: '-' },
  { name: 'Organization', desc: 'Default organization for mirrored repositories', default: '-' },
  { name: 'Visibility', desc: 'Default visibility for mirrored repositories', default: 'public' },
  { name: 'Starred Repos Org', desc: 'Organization for starred repositories', default: 'github' }
];
---

<MainLayout title="Configuration - Gitea Mirror">
  <main class="max-w-5xl mx-auto px-4 py-12">
    <div class="sticky top-4 z-10 mb-6">
      <a
        href="/docs/"
        class="inline-flex items-center gap-2 px-3 py-1.5 rounded-md bg-card text-foreground hover:bg-muted transition-colors border border-border focus:ring-2 focus:ring-ring outline-none"
      >
        <span aria-hidden="true">&larr;</span> Back to Documentation
      </a>
    </div>

    <article class="bg-card rounded-2xl shadow-lg p-6 md:p-8 border border-border">
      <!-- Header -->
      <div class="mb-12 space-y-4">
        <div class="flex items-center gap-2 text-sm text-muted-foreground mb-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          <span>Configuration Guide</span>
        </div>
        <h1 class="text-4xl font-bold tracking-tight">Gitea Mirror Configuration</h1>
        <p class="text-lg text-muted-foreground leading-relaxed max-w-4xl">
          This guide provides detailed information on how to configure Gitea Mirror for your environment.
        </p>
      </div>

      <!-- Configuration Methods -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Configuration Methods</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <div class="bg-card rounded-lg border border-border p-6 hover:border-primary/50 transition-colors">
            <div class="flex items-start gap-4">
              <div class="text-2xl">🔧</div>
              <div>
                <h3 class="font-semibold text-lg mb-2">Environment Variables</h3>
                <p class="text-sm text-muted-foreground">Set configuration options through environment variables for automated deployments</p>
              </div>
            </div>
          </div>
          
          <div class="bg-card rounded-lg border border-border p-6 hover:border-primary/50 transition-colors">
            <div class="flex items-start gap-4">
              <div class="text-2xl">🖥️</div>
              <div>
                <h3 class="font-semibold text-lg mb-2">Web UI</h3>
                <p class="text-sm text-muted-foreground">Configure the application through the web interface after installation</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Environment Variables -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Environment Variables</h2>
        
        <p class="text-muted-foreground mb-6">The following environment variables can be used to configure Gitea Mirror:</p>
        
        <div class="overflow-x-auto">
          <table class="w-full border-collapse">
            <thead>
              <tr class="border-b border-border">
                <th class="text-left py-3 px-4 font-semibold">Variable</th>
                <th class="text-left py-3 px-4 font-semibold">Description</th>
                <th class="text-left py-3 px-4 font-semibold">Default</th>
                <th class="text-left py-3 px-4 font-semibold">Example</th>
              </tr>
            </thead>
            <tbody>
              {envVars.map((v, i) => (
                <tr class={`border-b border-border/50 hover:bg-muted/30 ${i === envVars.length - 1 ? 'border-b-0' : ''}`}>
                  <td class="py-3 px-4">
                    <code class="text-sm bg-muted px-1.5 py-0.5 rounded">{v.name}</code>
                  </td>
                  <td class="py-3 px-4 text-sm text-muted-foreground">{v.desc}</td>
                  <td class="py-3 px-4 text-sm"><code>{v.default}</code></td>
                  <td class="py-3 px-4 text-sm"><code>{v.example}</code></td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <!-- Security Note -->
        <div class="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4 mt-6">
          <div class="flex gap-3">
            <div class="text-amber-600 dark:text-amber-500">
              <svg class="w-5 h-5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-amber-600 dark:text-amber-500 mb-1">Security Note</h4>
              <p class="text-sm">The application will automatically generate a secure random <code class="bg-amber-500/10 px-1 py-0.5 rounded">JWT_SECRET</code> on first run if one isn't provided. This generated secret is stored in the data directory for persistence across container restarts.</p>
              <p class="text-sm mt-2">While this auto-generation feature provides good security by default, you can still explicitly set your own <code class="bg-amber-500/10 px-1 py-0.5 rounded">JWT_SECRET</code> for complete control over your deployment.</p>
            </div>
          </div>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Web UI Configuration -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Web UI Configuration</h2>
        
        <p class="text-muted-foreground mb-6">After installing and starting Gitea Mirror, you can configure it through the web interface:</p>
        
        <div class="bg-gradient-to-r from-primary/5 to-transparent rounded-lg p-6 border-l-4 border-primary mb-8">
          <ol class="space-y-3">
            {[
              'Navigate to <code class="bg-muted px-1.5 py-0.5 rounded text-sm">http://your-server:port/</code>',
              'If this is your first time, you\'ll be guided through creating an admin account',
              'Log in with your credentials',
              'Go to the Configuration page'
            ].map((step, i) => (
              <li class="flex gap-3">
                <span class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold">{i + 1}</span>
                <span set:html={step}></span>
              </li>
            ))}
          </ol>
        </div>

        <!-- GitHub Configuration -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4">GitHub Configuration</h3>
          <p class="text-muted-foreground mb-4">The GitHub configuration section allows you to connect to GitHub and specify which repositories to mirror.</p>
          
          <div class="overflow-x-auto mb-6">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-border">
                  <th class="text-left py-3 px-4 font-semibold">Option</th>
                  <th class="text-left py-3 px-4 font-semibold">Description</th>
                  <th class="text-left py-3 px-4 font-semibold">Default</th>
                </tr>
              </thead>
              <tbody>
                {githubOptions.map((opt, i) => (
                  <tr class={`border-b border-border/50 hover:bg-muted/30 ${i === githubOptions.length - 1 ? 'border-b-0' : ''}`}>
                    <td class="py-3 px-4 font-medium">{opt.name}</td>
                    <td class="py-3 px-4 text-sm text-muted-foreground">{opt.desc}</td>
                    <td class="py-3 px-4 text-sm"><code>{opt.default}</code></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <!-- GitHub Token Permissions -->
          <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
            <div class="flex gap-3">
              <div class="text-blue-600 dark:text-blue-500">
                <svg class="w-5 h-5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-blue-600 dark:text-blue-500 mb-2">Required Permissions</h4>
                <p class="text-sm mb-3">You need to create a <span class="font-semibold">Classic GitHub PAT Token</span> with the following scopes:</p>
                <ul class="space-y-1 text-sm">
                  <li class="flex gap-2">
                    <span class="text-blue-600 dark:text-blue-500">•</span>
                    <span><code class="bg-blue-500/10 px-1 py-0.5 rounded">repo</code> - Full control of private repositories</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-blue-600 dark:text-blue-500">•</span>
                    <span><code class="bg-blue-500/10 px-1 py-0.5 rounded">admin:org</code> - Full control of orgs and teams, read and write org projects</span>
                  </li>
                </ul>
                <p class="text-sm mt-2">The organization access is required for mirroring organization repositories.</p>
              </div>
            </div>
          </div>

          <div class="pl-4 border-l-2 border-primary/20">
            <h5 class="font-semibold mb-3">To create a GitHub token:</h5>
            <ol class="space-y-2 text-sm">
              <li>Go to <a href="https://github.com/settings/tokens" class="text-primary hover:underline">GitHub Settings > Developer settings > Personal access tokens</a></li>
              <li>Click "Generate new token"</li>
              <li>Select the required permissions</li>
              <li>Copy the generated token and paste it into Gitea Mirror</li>
            </ol>
          </div>
        </div>

        <!-- Gitea Configuration -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4">Gitea Configuration</h3>
          <p class="text-muted-foreground mb-4">The Gitea configuration section allows you to connect to your Gitea instance and specify how repositories should be mirrored.</p>
          
          <div class="overflow-x-auto mb-6">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-border">
                  <th class="text-left py-3 px-4 font-semibold">Option</th>
                  <th class="text-left py-3 px-4 font-semibold">Description</th>
                  <th class="text-left py-3 px-4 font-semibold">Default</th>
                </tr>
              </thead>
              <tbody>
                {giteaOptions.map((opt, i) => (
                  <tr class={`border-b border-border/50 hover:bg-muted/30 ${i === giteaOptions.length - 1 ? 'border-b-0' : ''}`}>
                    <td class="py-3 px-4 font-medium">{opt.name}</td>
                    <td class="py-3 px-4 text-sm text-muted-foreground">{opt.desc}</td>
                    <td class="py-3 px-4 text-sm"><code>{opt.default}</code></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <!-- Mirror Strategies -->
          <div class="bg-gradient-to-r from-primary/5 to-transparent rounded-lg p-6 border-l-4 border-primary mb-6">
            <h4 class="font-semibold text-lg mb-4">Mirror Strategies</h4>
            <p class="text-sm text-muted-foreground mb-4">Choose how your repositories will be organized in Gitea:</p>
            
            <div class="space-y-4">
              <div class="bg-card rounded-lg border border-border p-4">
                <h5 class="font-semibold text-base mb-2 flex items-center gap-2">
                  <span class="text-blue-600 dark:text-blue-500">📁</span>
                  Preserve GitHub Structure
                </h5>
                <p class="text-sm text-muted-foreground mb-2">Maintains the exact structure from GitHub:</p>
                <ul class="space-y-1 text-sm ml-4">
                  <li class="flex gap-2">
                    <span class="text-muted-foreground">•</span>
                    <span>Personal repos → Your Gitea username</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-muted-foreground">•</span>
                    <span>Organization repos → Same organization name in Gitea</span>
                  </li>
                </ul>
              </div>

              <div class="bg-card rounded-lg border border-border p-4">
                <h5 class="font-semibold text-base mb-2 flex items-center gap-2">
                  <span class="text-purple-600 dark:text-purple-500">🏢</span>
                  Single Organization
                </h5>
                <p class="text-sm text-muted-foreground mb-2">Consolidates all repositories into one organization:</p>
                <ul class="space-y-1 text-sm ml-4">
                  <li class="flex gap-2">
                    <span class="text-muted-foreground">•</span>
                    <span>All repos → One designated organization</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-muted-foreground">•</span>
                    <span>Requires setting "Organization" field</span>
                  </li>
                </ul>
              </div>

              <div class="bg-card rounded-lg border border-border p-4">
                <h5 class="font-semibold text-base mb-2 flex items-center gap-2">
                  <span class="text-green-600 dark:text-green-500">👤</span>
                  Flat User Structure
                </h5>
                <p class="text-sm text-muted-foreground mb-2">Mirrors all repositories under your user account:</p>
                <ul class="space-y-1 text-sm ml-4">
                  <li class="flex gap-2">
                    <span class="text-muted-foreground">•</span>
                    <span>All repos → Your Gitea username</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-muted-foreground">•</span>
                    <span>No organizations needed</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div class="bg-amber-500/10 border border-amber-500/20 rounded-lg p-3 mt-4">
              <p class="text-sm">
                <span class="font-semibold">Note:</span> Starred repositories are always mirrored to the "Starred Repos Org" (default: "starred") regardless of the chosen strategy.
              </p>
            </div>
          </div>

          <div class="pl-4 border-l-2 border-primary/20">
            <h5 class="font-semibold mb-3">To create a Gitea access token:</h5>
            <ol class="space-y-2 text-sm">
              <li>Log in to your Gitea instance</li>
              <li>Go to Settings > Applications</li>
              <li>Under "Generate New Token", enter a name for your token</li>
              <li>Click "Generate Token"</li>
              <li>Copy the generated token and paste it into Gitea Mirror</li>
            </ol>
          </div>
        </div>

        <!-- Schedule Configuration -->
        <div>
          <h3 class="text-xl font-semibold mb-4">Schedule Configuration</h3>
          <p class="text-muted-foreground mb-4">You can configure automatic mirroring on a schedule:</p>
          
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-border">
                  <th class="text-left py-3 px-4 font-semibold">Option</th>
                  <th class="text-left py-3 px-4 font-semibold">Description</th>
                  <th class="text-left py-3 px-4 font-semibold">Default</th>
                </tr>
              </thead>
              <tbody>
                <tr class="border-b border-border/50 hover:bg-muted/30">
                  <td class="py-3 px-4 font-medium">Enable Scheduling</td>
                  <td class="py-3 px-4 text-sm text-muted-foreground">Enable automatic mirroring</td>
                  <td class="py-3 px-4 text-sm"><code>false</code></td>
                </tr>
                <tr class="hover:bg-muted/30">
                  <td class="py-3 px-4 font-medium">Interval (seconds)</td>
                  <td class="py-3 px-4 text-sm text-muted-foreground">Time between mirroring operations</td>
                  <td class="py-3 px-4 text-sm"><code>3600</code> (1 hour)</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Advanced Configuration -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Advanced Configuration</h2>


        <!-- Database Management -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4">Database Management</h3>
          <p class="text-muted-foreground mb-4">Gitea Mirror includes several database management tools that can be run from the command line:</p>
          
          <div class="bg-muted/30 rounded-lg p-4 overflow-x-auto">
            <pre class="text-sm whitespace-pre-wrap break-all"><code>{`# Initialize the database (only if it doesn't exist)
bun run init-db

# Check database status
bun run check-db

# Fix database location issues
bun run fix-db

# Reset all users (for testing signup flow)
bun run reset-users

# Remove database files completely
bun run cleanup-db`}</code></pre>
          </div>
        </div>

        <!-- Event Management -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4">Event Management</h3>
          <p class="text-muted-foreground mb-4">Events in Gitea Mirror (such as repository mirroring operations) are stored in the SQLite database and can be viewed in the Activity Log page.</p>
          
          <div class="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
            <div class="flex gap-3">
              <div class="text-green-600 dark:text-green-500">
                <svg class="w-5 h-5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div>
                <p class="text-sm mb-2">Event Management Features:</p>
                <ul class="space-y-1 text-sm">
                  <li class="flex gap-2">
                    <span class="text-green-600 dark:text-green-500">•</span>
                    <span>View all events with filtering by type, status, and search</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-green-600 dark:text-green-500">•</span>
                    <span>Real-time updates via Server-Sent Events (SSE)</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-green-600 dark:text-green-500">•</span>
                    <span>Clean up old events using the cleanup button in the Activity Log</span>
                  </li>
                  <li class="flex gap-2">
                    <span class="text-green-600 dark:text-green-500">•</span>
                    <span>Automatic cleanup with configurable retention period</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Automatic Recovery System -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4">Automatic Recovery System</h3>
          <p class="text-muted-foreground mb-4">Gitea Mirror includes a robust recovery system that automatically handles interrupted operations:</p>
          
          <div class="bg-card rounded-lg border border-border p-6">
            <ul class="space-y-3">
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <div>
                  <span class="font-semibold">Startup Recovery:</span>
                  <p class="text-sm text-muted-foreground mt-1">Automatically recovers interrupted jobs when the application starts</p>
                </div>
              </li>
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <div>
                  <span class="font-semibold">Graceful Shutdown:</span>
                  <p class="text-sm text-muted-foreground mt-1">Saves job state before shutting down to enable recovery on restart</p>
                </div>
              </li>
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <div>
                  <span class="font-semibold">Job State Persistence:</span>
                  <p class="text-sm text-muted-foreground mt-1">Stores mirror job progress in the database for resilience</p>
                </div>
              </li>
            </ul>
            
            <div class="mt-4 bg-muted/30 rounded-lg p-4 overflow-x-auto">
              <p class="text-sm font-medium mb-2">Manual recovery tools:</p>
              <pre class="text-sm whitespace-pre-wrap break-all"><code>{`# Run startup recovery manually
bun run startup-recovery

# Fix interrupted jobs
bun scripts/fix-interrupted-jobs.ts

# Test recovery system
bun run test-recovery`}</code></pre>
            </div>
          </div>
        </div>

        <!-- Health Check Endpoint -->
        <div>
          <h3 class="text-xl font-semibold mb-4">Health Check Endpoint</h3>
          
          <div class="bg-card rounded-lg border border-border p-6">
            <h4 class="font-semibold mb-3">System Health Monitoring</h4>
            <p class="text-muted-foreground mb-4">Gitea Mirror includes a built-in health check endpoint at <code class="bg-muted px-1.5 py-0.5 rounded">/api/health</code> that provides:</p>
            
            <ul class="space-y-2 mb-6">
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <span>System status and uptime</span>
              </li>
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <span>Database connectivity check</span>
              </li>
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <span>Memory usage statistics</span>
              </li>
              <li class="flex gap-2">
                <span class="text-primary">✓</span>
                <span>Environment information</span>
              </li>
            </ul>
            
            <div class="bg-muted/30 rounded-lg p-4 overflow-x-auto">
              <pre class="text-sm whitespace-pre-wrap break-all"><code>{`# Basic check (returns 200 OK if healthy)
curl -I http://your-server:port/api/health

# Detailed health information (JSON)
curl http://your-server:port/api/health`}</code></pre>
            </div>
          </div>
        </div>
      </section>
    </article>
  </main>
</MainLayout>