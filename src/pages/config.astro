---
import '../styles/global.css';
import App, { MainLayout } from '@/components/layout/MainLayout';
import { ConfigTabs } from '@/components/config/ConfigTabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { db, configs } from '@/lib/db';
import ThemeScript from '@/components/theme/ThemeScript.astro';
import { Button } from '@/components/ui/button';
import type { SaveConfigApiRequest,SaveConfigApiResponse } from '@/types/config';
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>Configuration - Gitea Mirror</title>
    <ThemeScript />
  </head>
  <body>
    <App page='configuration' client:load />
  </body>
</html>
