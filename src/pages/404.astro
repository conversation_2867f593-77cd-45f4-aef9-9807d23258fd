---
import '../styles/global.css';
import ThemeScript from '@/components/theme/ThemeScript.astro';
import { NotFound } from '@/components/NotFound';

const generator = Astro.generator;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={generator} />
    <title>Page Not Found - Gitea Mirror</title>
    <ThemeScript />
  </head>
  <body>
    <NotFound client:load />
  </body>
</html>

<style>
  /* Floating animation for 404 text */
  :global(.animate-float) {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
</style>