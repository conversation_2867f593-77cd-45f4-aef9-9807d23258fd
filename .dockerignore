# Version control
.git
.gitignore
.github

# Node.js
node_modules
# We don't exclude bun.lock* as it's needed for the build
npm-debug.log
yarn-debug.log
yarn-error.log

# Build outputs
dist
build
.next
out

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# IDE and editor files
.idea
.vscode
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Test coverage
coverage
.nyc_output

# Docker
Dockerfile
.dockerignore
docker-compose.yml
docker-compose.*.yml

# Documentation
README.md
LICENSE
docs

# Temporary files
tmp
temp
*.tmp
*.temp

# Logs
logs
*.log

# Cache
.cache
.npm
